<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Feedback - <PERSON><PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
            min-height: 100vh;
            padding: 20px;
            margin: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            backdrop-filter: blur(10px);
            width: 100%;
            table-layout: fixed;
        }
        
        .header {
            background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 36px;
            position: relative;
            z-index: 1;
        }
        
        .header h1 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .feedback-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #00ff88;
        }
        
        .feedback-info h2 {
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .feedback-info h2::before {
            content: '💬';
            margin-right: 10px;
            font-size: 1.2em;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        /* Fallback for email clients that don't support grid */
        .info-grid-fallback {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 25px;
        }

        .info-grid-fallback td {
            width: 50%;
            vertical-align: top;
            padding: 10px;
        }
        
        .info-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
        }
        
        .info-item h3 {
            color: #00cc6a;
            font-size: 1.1em;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .info-item p {
            color: #555;
            font-size: 1em;
            word-break: break-word;
        }
        
        .rating-display {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .rating-stars {
            font-size: 1.5em;
            color: #ffd700;
            letter-spacing: 2px;
        }
        
        .rating-text {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #333;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
        }
        
        .feedback-type-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            text-transform: capitalize;
            letter-spacing: 0.5px;
            background: linear-gradient(135deg, #00ff88, #00cc6a);
            color: white;
        }
        
        .recommendation-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
        }

        .recommend-yes {
            background: #d4edda;
            color: #155724;
        }

        .recommend-yes::before {
            content: '✅';
            margin-right: 4px;
        }

        .recommend-no {
            background: #f8d7da;
            color: #721c24;
        }

        .recommend-no::before {
            content: '❌';
            margin-right: 4px;
        }
        
        .message-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            border: 1px solid #e9ecef;
        }
        
        .message-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .message-content {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #00ff88;
            font-size: 1.1em;
            line-height: 1.7;
            color: #444;
            white-space: pre-wrap;
        }
        
        .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .footer h3 {
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .contact-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .contact-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            backdrop-filter: blur(5px);
        }
        
        .contact-item h4 {
            color: #00ff88;
            margin-bottom: 8px;
            font-size: 1.1em;
        }
        
        .contact-item a {
            color: white;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .contact-item a:hover {
            color: #00ff88;
        }
        
        .appreciation-box {
            background: linear-gradient(135deg, #00ff88, #00cc6a);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 25px 0;
            text-align: center;
            font-weight: 600;
        }
        
        .appreciation-box h3 {
            margin-bottom: 10px;
            font-size: 1.4em;
        }
        
        .timestamp {
            background: #e9ecef;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            text-align: center;
            color: #666;
            font-size: 0.95em;
        }
        
        .emoji-large {
            font-size: 2em;
            margin: 0 10px;
            display: inline-block;
            vertical-align: middle;
        }

        /* Text improvements */
        h1, h2, h3, h4, h5, h6 {
            margin-top: 0;
            font-weight: bold;
        }

        p {
            margin: 0 0 16px 0;
        }

        /* Link styling */
        a {
            color: #00cc6a;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        /* Image responsiveness */
        img {
            max-width: 100%;
            height: auto;
            display: block;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .container {
                margin: 0;
                border-radius: 15px;
                max-width: 100%;
            }

            .header {
                padding: 25px 20px;
            }

            .header h1 {
                font-size: 1.8em;
                line-height: 1.2;
            }

            .header p {
                font-size: 1em;
            }

            .logo {
                width: 60px;
                height: 60px;
                font-size: 28px;
                margin-bottom: 15px;
            }

            .content {
                padding: 25px 15px;
            }

            .feedback-info {
                padding: 20px 15px;
                margin-bottom: 20px;
            }

            .feedback-info h2 {
                font-size: 1.4em;
                margin-bottom: 15px;
            }

            .info-grid {
                grid-template-columns: 1fr;
                gap: 15px;
                margin-bottom: 20px;
            }

            .info-item {
                padding: 15px;
            }

            .info-item h3 {
                font-size: 1em;
                margin-bottom: 6px;
            }

            .info-item p {
                font-size: 0.9em;
            }

            .contact-info {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .contact-item {
                padding: 12px;
            }

            .contact-item h4 {
                font-size: 1em;
                margin-bottom: 6px;
            }

            .rating-display {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .rating-stars {
                font-size: 1.2em;
            }

            .rating-text {
                font-size: 0.8em;
                padding: 3px 10px;
            }

            .feedback-type-badge {
                font-size: 0.8em;
                padding: 6px 12px;
            }

            .recommendation-badge {
                font-size: 0.8em;
                padding: 6px 12px;
            }

            .message-section {
                padding: 20px 15px;
                margin: 20px 0;
            }

            .message-section h3 {
                font-size: 1.2em;
                margin-bottom: 12px;
            }

            .message-content {
                padding: 15px;
                font-size: 1em;
                line-height: 1.6;
            }

            .appreciation-box {
                padding: 15px;
                margin: 20px 0;
            }

            .appreciation-box h3 {
                font-size: 1.2em;
                margin-bottom: 8px;
            }

            .footer {
                padding: 25px 15px;
            }

            .footer h3 {
                font-size: 1.3em;
                margin-bottom: 15px;
            }

            .timestamp {
                padding: 12px;
                font-size: 0.9em;
                margin-top: 15px;
            }

            .emoji-large {
                font-size: 1.5em;
                margin: 0 5px;
            }
        }

        /* Extra small devices */
        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.5em;
            }

            .header p {
                font-size: 0.9em;
            }

            .logo {
                width: 50px;
                height: 50px;
                font-size: 24px;
            }

            .content {
                padding: 20px 10px;
            }

            .feedback-info {
                padding: 15px 10px;
            }

            .info-item {
                padding: 12px;
            }

            .message-content {
                padding: 12px;
                font-size: 0.95em;
            }

            .footer {
                padding: 20px 10px;
            }

            .contact-item {
                padding: 10px;
            }
        }

        /* Email client specific fixes */
        @media screen and (max-width: 600px) {
            .container {
                width: 100% !important;
                max-width: 100% !important;
            }

            .info-grid {
                display: block !important;
            }

            .info-item {
                display: block !important;
                width: 100% !important;
                margin-bottom: 15px !important;
            }

            .contact-info {
                display: block !important;
            }

            .contact-item {
                display: block !important;
                width: 100% !important;
                margin-bottom: 15px !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🐺</div>
            <h1>User Feedback Received</h1>
            <p>Cyber Wolf Cybersecurity Training Platform</p>
        </div>
        
        <!-- Content -->
        <div class="content">
            <!-- Feedback Information -->
            <div class="feedback-info">
                <h2>New Feedback Submission</h2>
                
                <div class="info-grid">
                    <div class="info-item">
                        <h3>👤 User Name</h3>
                        <p>{{user_name}}</p>
                    </div>
                    
                    <div class="info-item">
                        <h3>📧 Email Address</h3>
                        <p>{{user_email}}</p>
                    </div>
                    
                    <div class="info-item">
                        <h3>📋 Subject</h3>
                        <p>{{feedback_subject}}</p>
                    </div>
                    
                    <div class="info-item">
                        <h3>🏷️ Feedback Type</h3>
                        <p><span class="feedback-type-badge">{{feedback_type}}</span></p>
                    </div>
                    
                    <div class="info-item">
                        <h3>⭐ Rating</h3>
                        <div class="rating-display">
                            <span class="rating-stars">{{rating_stars}}</span>
                            <span class="rating-text">{{feedback_rating}}/5</span>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <h3>👍 Would Recommend</h3>
                        <p>
                            <span class="recommendation-badge recommend-yes" style="display: {{would_recommend == 'Yes' ? 'inline-flex' : 'none'}};">
                                {{would_recommend}}
                            </span>
                            <span class="recommendation-badge recommend-no" style="display: {{would_recommend == 'No' ? 'inline-flex' : 'none'}};">
                                {{would_recommend}}
                            </span>
                        </p>
                    </div>
                </div>
                
                <div class="info-grid">
                    <div class="info-item">
                        <h3>📞 Contact Permission</h3>
                        <p>{{allow_contact}}</p>
                    </div>
                </div>
            </div>
            
            <!-- Appreciation Message -->
            <div class="appreciation-box">
                <h3>🙏 Thank You for Your Feedback!</h3>
                <p>Your input helps us improve and provide better cybersecurity education</p>
            </div>

            <!-- Feedback Message -->
            <div class="message-section">
                <h3>💭 Detailed Feedback</h3>
                <div class="message-content">{{feedback_message}}</div>
            </div>

            <!-- Timestamp -->
            <div class="timestamp">
                <strong>📅 Submitted on:</strong> {{submission_time}}
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <h3>🚀 Cyber Wolf Team Response</h3>
            
            <div class="contact-info">
                <div class="contact-item">
                    <h4>📧 Email Support</h4>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                
                <div class="contact-item">
                    <h4>📱 Phone Support</h4>
                    <a href="tel:+916374344424">+91 6374 344 424</a><br>
                    <a href="tel:+916379869678">+91 6379 869 678</a>
                </div>
                
                <div class="contact-item">
                    <h4>💬 WhatsApp Community</h4>
                    <a href="https://whatsapp.com/channel/0029VbAmBt7GufIqh9iwND05" target="_blank">Join Community</a>
                </div>
            </div>
            
            <p style="margin-top: 20px; opacity: 0.8;">
                <span class="emoji-large">🛡️</span>
                Empowering the next generation of cybersecurity professionals
                <span class="emoji-large">🚀</span>
            </p>
            
            <p style="margin-top: 15px; font-size: 0.9em; opacity: 0.7;">
                We value every piece of feedback and strive to continuously improve our platform
            </p>
            
            <p style="margin-top: 10px; font-size: 0.9em; opacity: 0.7;">
                © 2024 Cyber Wolf - Advanced Cybersecurity Training Platform
            </p>
        </div>
    </div>
</body>
</html>
