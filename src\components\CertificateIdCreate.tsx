import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { useToast } from '@/hooks/use-toast';
import { Shield, User, Calendar, MapPin, GraduationCap, Lock, Eye, EyeOff, Award, Plus, Copy, Check } from 'lucide-react';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { signInWithEmailAndPassword, signOut } from 'firebase/auth';
import { db, auth } from '@/lib/firebase';

// Form validation schema
const certificateSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  age: z.string().min(1, "Age is required").refine((val) => {
    const age = parseInt(val);
    return age >= 16 && age <= 100;
  }, "Age must be between 16 and 100"),
  courseName: z.string().min(2, "Course name is required"),
  college: z.string().min(2, "College name is required"),
  country: z.string().min(2, "Country is required"),
  state: z.string().min(2, "State is required"),
  courseStartDate: z.string().optional(),
  courseJoiningDate: z.string().optional(),
  courseEndDate: z.string().min(1, "Course end date is required"),
  certificateProvideDate: z.string().min(1, "Certificate provide date is required"),
});

type CertificateFormData = z.infer<typeof certificateSchema>;

// Admin credentials
const ADMIN_EMAIL = "<EMAIL>";
const ADMIN_PASSWORD = "Tamilselvanadmin6379869678@";

// Security configuration
const MAX_LOGIN_ATTEMPTS = 3;
const LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes in milliseconds
const SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes in milliseconds

const CertificateIdCreate = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [loginEmail, setLoginEmail] = useState("");
  const [loginPassword, setLoginPassword] = useState("");
  const [generatedCertificateId, setGeneratedCertificateId] = useState("");
  const [isCopied, setIsCopied] = useState(false);

  // Security states
  const [loginAttempts, setLoginAttempts] = useState(0);
  const [isLockedOut, setIsLockedOut] = useState(false);
  const [lockoutEndTime, setLockoutEndTime] = useState<number | null>(null);
  const [sessionStartTime, setSessionStartTime] = useState<number | null>(null);
  const [lastActivity, setLastActivity] = useState<number>(Date.now());
  const [showSecurityWarning, setShowSecurityWarning] = useState(false);

  const { toast } = useToast();

  const form = useForm<CertificateFormData>({
    resolver: zodResolver(certificateSchema),
    defaultValues: {
      name: "",
      age: "",
      courseName: "",
      college: "",
      country: "",
      state: "",
      courseStartDate: "",
      courseJoiningDate: "",
      courseEndDate: "",
      certificateProvideDate: "",
    },
  });

  // Security utility functions
  const checkLockoutStatus = () => {
    if (lockoutEndTime && Date.now() < lockoutEndTime) {
      return true;
    } else if (lockoutEndTime && Date.now() >= lockoutEndTime) {
      // Reset lockout
      setIsLockedOut(false);
      setLockoutEndTime(null);
      setLoginAttempts(0);
      return false;
    }
    return isLockedOut;
  };

  const updateActivity = () => {
    setLastActivity(Date.now());
  };

  const checkSessionTimeout = () => {
    if (sessionStartTime && (Date.now() - lastActivity) > SESSION_TIMEOUT) {
      handleLogout();
      toast({
        title: "Session Expired",
        description: "Your session has expired due to inactivity. Please login again.",
        variant: "destructive",
      });
      return true;
    }
    return false;
  };

  const logSecurityEvent = (event: string, details?: any) => {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      event,
      details,
      userAgent: navigator.userAgent,
      ip: 'client-side', // In production, this would be logged server-side
    };
    console.warn('Security Event:', logEntry);

    // In production, send to security monitoring service
    // securityService.logEvent(logEntry);
  };

  // Session monitoring
  useEffect(() => {
    if (isAuthenticated) {
      const interval = setInterval(() => {
        if (checkSessionTimeout()) {
          clearInterval(interval);
        }
      }, 60000); // Check every minute

      // Activity listeners
      const handleActivity = () => updateActivity();

      document.addEventListener('mousedown', handleActivity);
      document.addEventListener('keydown', handleActivity);
      document.addEventListener('scroll', handleActivity);
      document.addEventListener('touchstart', handleActivity);

      return () => {
        clearInterval(interval);
        document.removeEventListener('mousedown', handleActivity);
        document.removeEventListener('keydown', handleActivity);
        document.removeEventListener('scroll', handleActivity);
        document.removeEventListener('touchstart', handleActivity);
      };
    }
  }, [isAuthenticated, lastActivity, sessionStartTime]);

  // Lockout timer
  useEffect(() => {
    if (isLockedOut && lockoutEndTime) {
      const timer = setTimeout(() => {
        setIsLockedOut(false);
        setLockoutEndTime(null);
        setLoginAttempts(0);
        toast({
          title: "Account Unlocked",
          description: "You can now attempt to login again.",
        });
      }, lockoutEndTime - Date.now());

      return () => clearTimeout(timer);
    }
  }, [isLockedOut, lockoutEndTime]);

  // Generate 16-digit certificate ID with CWT prefix
  const generateCertificateId = (): string => {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    const id = (timestamp + random).slice(-13); // Take last 13 digits
    return `CWT${id}`;
  };

  // Copy certificate ID to clipboard
  const copyCertificateId = async (certificateId: string) => {
    try {
      await navigator.clipboard.writeText(certificateId);
      setIsCopied(true);
      toast({
        title: "Copied!",
        description: "Certificate ID copied to clipboard",
      });

      // Reset copy status after 2 seconds
      setTimeout(() => {
        setIsCopied(false);
      }, 2000);
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy certificate ID. Please copy manually.",
        variant: "destructive",
      });
    }
  };

  // Admin authentication
  const handleAdminLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsAuthenticating(true);

    try {
      // First validate credentials locally
      if (loginEmail.trim() !== ADMIN_EMAIL || loginPassword !== ADMIN_PASSWORD) {
        throw new Error("Invalid admin credentials. Please check your email and password.");
      }

      // If Firebase is configured, try Firebase authentication
      if (auth) {
        try {
          await signInWithEmailAndPassword(auth, loginEmail, loginPassword);
        } catch (firebaseError: any) {
          // If Firebase auth fails, check if it's because user doesn't exist
          if (firebaseError.code === 'auth/user-not-found') {
            throw new Error("Admin user not found in Firebase. Please create the admin user in Firebase Console first.");
          } else if (firebaseError.code === 'auth/wrong-password') {
            throw new Error("Wrong password. Please check your credentials.");
          } else if (firebaseError.code === 'auth/invalid-email') {
            throw new Error("Invalid email format.");
          } else if (firebaseError.code === 'auth/too-many-requests') {
            throw new Error("Too many failed attempts. Please try again later.");
          } else {
            throw new Error(`Firebase Authentication Error: ${firebaseError.message}`);
          }
        }
      } else {
        // If Firebase is not configured, show warning but allow local authentication
        console.warn("Firebase not configured. Using local authentication only.");
        toast({
          title: "Warning",
          description: "Firebase not configured. Some features may not work properly.",
          variant: "destructive",
        });
      }

      setIsAuthenticated(true);
      toast({
        title: "Authentication Successful",
        description: "Welcome to Certificate Management System",
      });
    } catch (error: any) {
      console.error("Authentication error:", error);
      toast({
        title: "Authentication Failed",
        description: error.message || "Invalid admin credentials. Access denied.",
        variant: "destructive",
      });
    } finally {
      setIsAuthenticating(false);
    }
  };

  // Admin logout
  const handleLogout = async () => {
    try {
      await signOut(auth);
      setIsAuthenticated(false);
      setLoginEmail("");
      setLoginPassword("");
      form.reset();
      setGeneratedCertificateId("");
      toast({
        title: "Logged Out",
        description: "Successfully logged out from admin panel",
      });
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  // Create certificate
  const onSubmit = async (data: CertificateFormData) => {
    setIsCreating(true);

    try {
      if (!db) {
        throw new Error("Firebase Firestore not configured. Please check FIREBASE_SETUP.md for setup instructions.");
      }

      const certificateId = generateCertificateId();

      const certificateData = {
        certificateId,
        name: data.name,
        age: parseInt(data.age),
        courseName: data.courseName,
        college: data.college,
        country: data.country,
        state: data.state,
        courseStartDate: data.courseStartDate || "",
        courseJoiningDate: data.courseJoiningDate || "",
        courseEndDate: data.courseEndDate,
        certificateProvideDate: data.certificateProvideDate,
        createdAt: serverTimestamp(),
        createdBy: "admin",
        status: "active"
      };

      await addDoc(collection(db, 'certificates'), certificateData);

      setGeneratedCertificateId(certificateId);
      form.reset();

      toast({
        title: "Certificate Created Successfully!",
        description: `Certificate ID: ${certificateId}`,
      });

    } catch (error: any) {
      console.error("Error creating certificate:", error);

      let errorMessage = "Failed to create certificate. Please try again.";
      let errorTitle = "Error";

      if (error.code === 'permission-denied') {
        errorTitle = "Permission Denied";
        errorMessage = "Firestore permission denied. Please update security rules in Firebase Console. Check FIREBASE_SETUP.md for instructions.";
      } else if (error.code === 'unavailable') {
        errorTitle = "Service Unavailable";
        errorMessage = "Firebase service unavailable. Please check your internet connection and try again.";
      } else if (error.code === 'unauthenticated') {
        errorTitle = "Authentication Required";
        errorMessage = "User not authenticated. Please ensure admin user exists in Firebase Authentication.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: errorTitle,
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  // Admin login form
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-psyco-black-DEFAULT via-psyco-black-light to-psyco-black-DEFAULT flex items-center justify-center p-4">
        <Card className="w-full max-w-md glassmorphism">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center mb-4">
              <Shield className="h-8 w-8 text-white" />
            </div>
            <CardTitle className="text-2xl font-bold text-white">Admin Access</CardTitle>
            <CardDescription className="text-gray-300">
              Certificate Management System - Authorized Personnel Only
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleAdminLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-white flex items-center gap-2">
                  <User size={16} />
                  Admin Email
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={loginEmail}
                  onChange={(e) => setLoginEmail(e.target.value)}
                  placeholder="Enter admin email"
                  className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password" className="text-white flex items-center gap-2">
                  <Lock size={16} />
                  Admin Password
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={loginPassword}
                    onChange={(e) => setLoginPassword(e.target.value)}
                    placeholder="Enter admin password"
                    className="bg-white/10 border-white/20 text-white placeholder:text-gray-400 pr-10"
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 text-gray-400 hover:text-white"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                  </Button>
                </div>
              </div>
              <Button
                type="submit"
                className="w-full bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white"
                disabled={isAuthenticating}
              >
                {isAuthenticating ? "Authenticating..." : "Access Admin Panel"}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Certificate creation form
  return (
    <div className="min-h-screen bg-gradient-to-br from-psyco-black-DEFAULT via-psyco-black-light to-psyco-black-DEFAULT p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center">
              <Award className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-white">Certificate Management</h1>
              <p className="text-gray-400">Create and manage course certificates</p>
            </div>
          </div>
          
        </div>

        {/* Success Message */}
        {generatedCertificateId && (
          <Card className="glassmorphism mb-8 border-psyco-green-DEFAULT/50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center">
                    <Award className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white">Certificate Created Successfully!</h3>
                    <p className="text-psyco-green-DEFAULT font-mono text-lg">
                      Certificate ID: {generatedCertificateId}
                    </p>
                  </div>
                </div>
                <Button
                  onClick={() => copyCertificateId(generatedCertificateId)}
                  variant="outline"
                  size="sm"
                  className="border-psyco-green-DEFAULT/50 text-psyco-green-DEFAULT hover:bg-psyco-green-DEFAULT/10"
                >
                  {isCopied ? (
                    <>
                      <Check className="mr-2 h-4 w-4" />
                      Copied!
                    </>
                  ) : (
                    <>
                      <Copy className="mr-2 h-4 w-4" />
                      Copy ID
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Certificate Creation Form */}
        <Card className="glassmorphism">
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-white flex items-center gap-2">
              <Plus className="h-6 w-6" />
              Create New Certificate
            </CardTitle>
            <CardDescription className="text-gray-300">
              Fill in the details to generate a new certificate with unique ID
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Personal Information */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white border-b border-psyco-green-DEFAULT/30 pb-2">
                      Personal Information
                    </h3>
                    
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-white flex items-center gap-2">
                            <User size={16} />
                            Full Name *
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter full name"
                              className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="age"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-white flex items-center gap-2">
                            <Calendar size={16} />
                            Age *
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="Enter age"
                              className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="courseName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-white flex items-center gap-2">
                            <GraduationCap size={16} />
                            Course Name *
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter course name (e.g., Web Penetration Testing, Cybersecurity Fundamentals)"
                              className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                          <p className="text-gray-400 text-xs">
                            Common courses: Web Penetration Testing, Cybersecurity Fundamentals, Ethical Hacking, Network Security
                          </p>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="college"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-white flex items-center gap-2">
                            <GraduationCap size={16} />
                            College/Institution *
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter college name"
                              className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="country"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-white flex items-center gap-2">
                            <MapPin size={16} />
                            Country *
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter country"
                              className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="state"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-white flex items-center gap-2">
                            <MapPin size={16} />
                            State *
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter state"
                              className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Course Information */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white border-b border-psyco-green-DEFAULT/30 pb-2">
                      Course Information
                    </h3>

                    <FormField
                      control={form.control}
                      name="courseStartDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-white flex items-center gap-2">
                            <Calendar size={16} />
                            Course Start Date
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="date"
                              className="bg-white/10 border-white/20 text-white"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="courseJoiningDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-white flex items-center gap-2">
                            <Calendar size={16} />
                            Course Joining Date
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="date"
                              className="bg-white/10 border-white/20 text-white"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="courseEndDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-white flex items-center gap-2">
                            <Calendar size={16} />
                            Course End Date *
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="date"
                              className="bg-white/10 border-white/20 text-white"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="certificateProvideDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-white flex items-center gap-2">
                            <Award size={16} />
                            Certificate Provide Date *
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="date"
                              className="bg-white/10 border-white/20 text-white"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <Button
                  type="submit"
                  className="w-full bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white py-3 text-lg"
                  disabled={isCreating}
                >
                  {isCreating ? "Creating Certificate..." : "Create Certificate"}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Logout Button - Bottom Position */}
        <div className="mt-8 flex justify-center">
          <Button
            onClick={handleLogout}
            variant="outline"
            className="border-red-500/50 text-red-400 hover:bg-red-500/10 hover:border-red-400 px-8 py-3"
          >
            <User className="mr-2 h-4 w-4" />
            Logout from Admin Panel
          </Button>
          <Button
            onClick={() => window.location.href = '/admin-dashboard'}
            variant="outline"
            className="border-blue-500/50 text-blue-400 hover:bg-blue-500/10 hover:border-blue-400"
          >
            <Shield className="mr-2 h-4 w-4" />
            Admin Dashboard
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CertificateIdCreate;
