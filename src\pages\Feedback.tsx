import React, { useEffect, useState, useMemo, useCallback } from 'react';
import {
  MessageSquare,
  Star,
  Heart,
  Users,
  Lightbulb,
  ThumbsUp,
  TrendingUp,
  Award,
  CheckCircle,
  ArrowRight,
  Quote,
  Sparkles
} from 'lucide-react';
import FeedbackForm from '@/components/FeedbackForm';

const Feedback = () => {
  const [isFeedbackOpen, setIsFeedbackOpen] = useState(false);
  const [showAllTestimonials, setShowAllTestimonials] = useState(false);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const openFeedback = useCallback(() => setIsFeedbackOpen(true), []);
  const closeFeedback = useCallback(() => setIsFeedbackOpen(false), []);
  const toggleTestimonials = useCallback(() => setShowAllTestimonials(prev => !prev), []);

  const feedbackStats = useMemo(() => [
    {
      icon: Star,
      label: 'Average Rating',
      value: '4.8/5',
      color: 'from-yellow-500 to-orange-500',
      description: 'Based on student reviews',
      trend: '+0.2 this month'
    },
    {
      icon: Users,
      label: 'Total Reviews',
      value: '1,247',
      color: 'from-blue-500 to-purple-500',
      description: 'Community feedback',
      trend: '+89 this week'
    },
    {
      icon: Heart,
      label: 'Satisfaction Rate',
      value: '96%',
      color: 'from-pink-500 to-red-500',
      description: 'Student satisfaction',
      trend: '+3% improvement'
    },
    {
      icon: ThumbsUp,
      label: 'Would Recommend',
      value: '94%',
      color: 'from-green-500 to-teal-500',
      description: 'Recommendation rate',
      trend: 'Consistently high'
    },
  ], []);

  const testimonials = useMemo(() => [
    {
      name: "Rajesh Kumar",
      role: "Cybersecurity Student",
      rating: 5,
      comment: "Excellent course content and practical approach. The hands-on labs really helped me understand penetration testing concepts. The community support is outstanding!",
      avatar: "👨‍💻",
      date: "2 days ago",
      verified: true,
      course: "Web Penetration Testing"
    },
    {
      name: "Priya Sharma",
      role: "IT Professional",
      rating: 5,
      comment: "The instructors are knowledgeable and the community support is amazing. Highly recommend for anyone starting in cybersecurity. Best investment in my career!",
      avatar: "👩‍💼",
      date: "1 week ago",
      verified: true,
      course: "Ethical Hacking Fundamentals"
    },
    {
      name: "Arjun Patel",
      role: "Security Analyst",
      rating: 4,
      comment: "Great platform for learning ethical hacking. The course structure is well-organized and easy to follow. Looking forward to advanced courses!",
      avatar: "🧑‍🔬",
      date: "2 weeks ago",
      verified: true,
      course: "Cybersecurity Basics"
    },
    {
      name: "Sneha Reddy",
      role: "Software Developer",
      rating: 5,
      comment: "Amazing learning experience! The practical approach and real-world scenarios make complex concepts easy to understand.",
      avatar: "👩‍💻",
      date: "3 days ago",
      verified: true,
      course: "Secure Coding Practices"
    },
    {
      name: "Vikram Singh",
      role: "Network Administrator",
      rating: 5,
      comment: "Top-notch content and excellent support. The hands-on labs are incredibly valuable for practical learning.",
      avatar: "👨‍🔧",
      date: "5 days ago",
      verified: true,
      course: "Network Security"
    },
    {
      name: "Anita Gupta",
      role: "Cybersecurity Enthusiast",
      rating: 4,
      comment: "Great introduction to cybersecurity. The instructors explain complex topics in a simple and engaging way.",
      avatar: "👩‍🎓",
      date: "1 week ago",
      verified: true,
      course: "Introduction to Cybersecurity"
    }
  ], []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-psyco-black-DEFAULT via-psyco-black-light to-psyco-black-DEFAULT">
      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 px-6 md:px-12">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute w-96 h-96 bg-psyco-green-DEFAULT/10 rounded-full blur-3xl top-1/4 left-1/4"></div>
          <div className="absolute w-96 h-96 bg-psyco-green-DEFAULT/5 rounded-full blur-3xl bottom-1/4 right-1/4"></div>
        </div>
        
        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center mb-16">
            <div className="flex justify-center mb-8">
              <div className="w-20 h-20 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center">
                <MessageSquare className="h-10 w-10 text-white" />
              </div>
            </div>
            
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 animate-fade-in">
              YOUR FEEDBACK
              <span className="block text-psyco-green-DEFAULT">MATTERS</span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-300 mb-8 animate-fade-in max-w-4xl mx-auto">
              Help us improve our cybersecurity training platform. Share your experience, suggestions, and ideas with the Cyber Wolf community.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
              <button
                onClick={openFeedback}
                className="bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-bold py-4 px-12 rounded-lg transition-all duration-300 flex items-center text-xl btn-glow"
              >
                Share Your Feedback
                <MessageSquare className="ml-3 h-6 w-6" />
              </button>

              <div className="flex items-center text-gray-400 text-sm">
                <CheckCircle className="h-4 w-4 mr-2 text-psyco-green-DEFAULT" />
                <span>Anonymous & Secure</span>
              </div>
            </div>

            <div className="flex items-center justify-center space-x-8 text-gray-400 text-sm">
              <div className="flex items-center">
                <Star className="h-4 w-4 mr-1 text-yellow-400" />
                <span>4.8/5 Rating</span>
              </div>
              <div className="flex items-center">
                <Users className="h-4 w-4 mr-1 text-blue-400" />
                <span>1,247+ Reviews</span>
              </div>
              <div className="flex items-center">
                <TrendingUp className="h-4 w-4 mr-1 text-green-400" />
                <span>96% Satisfaction</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Feedback Stats */}
      <section className="py-16 px-6 md:px-12 bg-psyco-black-light">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Community Feedback Overview</h2>
            <p className="text-lg text-gray-400">What our students are saying about Cyber Wolf</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {feedbackStats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <div
                  key={index}
                  className="glassmorphism p-8 rounded-xl text-center hover:scale-105 transition-transform duration-200 group"
                >
                  <div className={`w-16 h-16 bg-gradient-to-r ${stat.color} rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-200`}>
                    <IconComponent className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-2">{stat.value}</h3>
                  <p className="text-gray-400 mb-2">{stat.label}</p>
                  <p className="text-xs text-gray-500 mb-1">{stat.description}</p>
                  <div className="flex items-center justify-center text-xs text-psyco-green-DEFAULT">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    <span>{stat.trend}</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Recent Testimonials */}
      <section className="py-20 px-6 md:px-12">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Recent Student Feedback</h2>
            <p className="text-lg text-gray-400">Real experiences from our cybersecurity community</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.slice(0, showAllTestimonials ? testimonials.length : 3).map((testimonial, index) => (
              <div
                key={index}
                className="glassmorphism p-8 rounded-xl hover:scale-105 transition-transform duration-200 group relative overflow-hidden"
              >
                {/* Quote Icon */}
                <div className="absolute top-4 right-4 opacity-20 group-hover:opacity-30 transition-opacity">
                  <Quote className="h-8 w-8 text-psyco-green-DEFAULT" />
                </div>

                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center text-2xl mr-4 group-hover:scale-110 transition-transform duration-200">
                    {testimonial.avatar}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center">
                      <h3 className="text-white font-bold">{testimonial.name}</h3>
                      {testimonial.verified && (
                        <CheckCircle className="h-4 w-4 ml-2 text-psyco-green-DEFAULT" />
                      )}
                    </div>
                    <p className="text-gray-400 text-sm">{testimonial.role}</p>
                    <p className="text-xs text-gray-500">{testimonial.date}</p>
                  </div>
                </div>

                <div className="flex items-center justify-between mb-4">
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${i < testimonial.rating ? 'text-yellow-400 fill-current' : 'text-gray-600'}`}
                      />
                    ))}
                  </div>
                  <span className="text-xs text-psyco-green-DEFAULT bg-psyco-green-DEFAULT/10 px-2 py-1 rounded-full">
                    {testimonial.course}
                  </span>
                </div>

                <p className="text-gray-300 italic leading-relaxed">"{testimonial.comment}"</p>

                {/* Hover Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-psyco-green-DEFAULT/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none"></div>
              </div>
            ))}
          </div>

          {/* View More Button */}
          <div className="text-center mt-12">
            <button
              onClick={toggleTestimonials}
              className="bg-transparent border-2 border-psyco-green-DEFAULT text-psyco-green-DEFAULT hover:bg-psyco-green-DEFAULT hover:text-white font-bold py-3 px-8 rounded-lg transition-all duration-200 flex items-center mx-auto"
            >
              {showAllTestimonials ? 'Show Less' : 'View More Reviews'}
              <ArrowRight className={`ml-2 h-5 w-5 transition-transform duration-200 ${showAllTestimonials ? 'rotate-180' : ''}`} />
            </button>
          </div>
        </div>
      </section>

      {/* Feedback Categories */}
      <section className="py-16 px-6 md:px-12 bg-psyco-black-light">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">What Can You Share?</h2>
            <p className="text-lg text-gray-400">We value all types of feedback</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              {
                icon: <Star size={32} />,
                title: "Course Content",
                description: "Share your thoughts on our cybersecurity courses and materials",
                color: "from-yellow-500 to-orange-500",
                count: "342 reviews"
              },
              {
                icon: <MessageSquare size={32} />,
                title: "General Feedback",
                description: "Any general comments or suggestions about our platform",
                color: "from-blue-500 to-purple-500",
                count: "189 reviews"
              },
              {
                icon: <Lightbulb size={32} />,
                title: "Suggestions",
                description: "Ideas for new features, courses, or improvements",
                color: "from-green-500 to-teal-500",
                count: "156 ideas"
              },
              {
                icon: <ThumbsUp size={32} />,
                title: "Website Experience",
                description: "Feedback about website usability and user experience",
                color: "from-pink-500 to-red-500",
                count: "98 reviews"
              },
              {
                icon: <Heart size={32} />,
                title: "Compliments",
                description: "Share what you love about Cyber Wolf",
                color: "from-purple-500 to-indigo-500",
                count: "267 compliments"
              },
              {
                icon: <Users size={32} />,
                title: "Community",
                description: "Feedback about our community and support system",
                color: "from-cyan-500 to-blue-500",
                count: "195 reviews"
              }
            ].map((category, index) => (
              <div
                key={index}
                className="glassmorphism p-6 rounded-xl text-center hover:scale-105 transition-transform duration-200 group cursor-pointer"
                onClick={openFeedback}
              >
                <div className={`w-16 h-16 bg-gradient-to-r ${category.color} rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-200`}>
                  <div className="text-white">
                    {category.icon}
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-3 group-hover:text-psyco-green-DEFAULT transition-colors duration-200">{category.title}</h3>
                <p className="text-gray-400 text-sm mb-3">{category.description}</p>
                <div className="flex items-center justify-center text-xs text-psyco-green-DEFAULT">
                  <Sparkles className="h-3 w-3 mr-1" />
                  <span>{category.count}</span>
                </div>

                {/* Hover Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-psyco-green-DEFAULT/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-xl pointer-events-none"></div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Recent Feedback Highlights */}
      <section className="py-16 px-6 md:px-12">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Recent Feedback Highlights</h2>
            <p className="text-lg text-gray-400">What our community is saying this week</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              {
                type: "Suggestion",
                message: "Would love to see more advanced malware analysis courses!",
                response: "Great idea! We're working on it.",
                status: "In Progress",
                color: "from-blue-500 to-purple-500"
              },
              {
                type: "Compliment",
                message: "The hands-on labs are incredibly well-designed and practical.",
                response: "Thank you! We're glad you enjoyed them.",
                status: "Appreciated",
                color: "from-pink-500 to-red-500"
              },
              {
                type: "Bug Report",
                message: "Minor issue with video playback on mobile devices.",
                response: "Fixed in the latest update!",
                status: "Resolved",
                color: "from-orange-500 to-red-500"
              }
            ].map((feedback, index) => (
              <div key={index} className="glassmorphism p-6 rounded-xl">
                <div className="flex items-center justify-between mb-4">
                  <span className={`px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r ${feedback.color} text-white`}>
                    {feedback.type}
                  </span>
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    feedback.status === 'Resolved' ? 'bg-green-100 text-green-800' :
                    feedback.status === 'In Progress' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-blue-100 text-blue-800'
                  }`}>
                    {feedback.status}
                  </span>
                </div>
                <p className="text-gray-300 mb-3 italic">"{feedback.message}"</p>
                <div className="border-l-2 border-psyco-green-DEFAULT pl-3">
                  <p className="text-sm text-psyco-green-DEFAULT font-medium">Team Response:</p>
                  <p className="text-sm text-gray-400">{feedback.response}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 px-6 md:px-12">
        <div className="max-w-4xl mx-auto text-center">
          <div className="glassmorphism p-12 rounded-2xl relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute w-32 h-32 bg-psyco-green-DEFAULT rounded-full -top-16 -left-16"></div>
              <div className="absolute w-24 h-24 bg-psyco-green-DEFAULT rounded-full -bottom-12 -right-12"></div>
            </div>

            <div className="relative z-10">
              <div className="flex justify-center mb-6">
                <div className="w-16 h-16 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center">
                  <MessageSquare className="h-8 w-8 text-white" />
                </div>
              </div>

              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">Ready to Share Your Experience?</h2>
              <p className="text-xl text-gray-300 mb-8">
                Your feedback helps us create better cybersecurity education for everyone.
                Join thousands of students who have shared their thoughts with us.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
                <button
                  onClick={openFeedback}
                  className="bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-bold py-4 px-12 rounded-lg transition-all duration-300 flex items-center text-xl btn-glow group"
                >
                  Submit Feedback Now
                  <MessageSquare className="ml-3 h-6 w-6 group-hover:scale-110 transition-transform" />
                </button>

                <div className="flex items-center text-gray-400 text-sm">
                  <Award className="h-4 w-4 mr-2 text-yellow-400" />
                  <span>Help us maintain our 4.8★ rating</span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div className="flex items-center justify-center text-gray-400 text-sm">
                  <CheckCircle className="h-4 w-4 mr-2 text-psyco-green-DEFAULT" />
                  <span>100% Anonymous</span>
                </div>
                <div className="flex items-center justify-center text-gray-400 text-sm">
                  <CheckCircle className="h-4 w-4 mr-2 text-psyco-green-DEFAULT" />
                  <span>Quick & Easy</span>
                </div>
                <div className="flex items-center justify-center text-gray-400 text-sm">
                  <CheckCircle className="h-4 w-4 mr-2 text-psyco-green-DEFAULT" />
                  <span>Helps Improve Platform</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Feedback Form Modal */}
      <FeedbackForm isOpen={isFeedbackOpen} onClose={closeFeedback} />
    </div>
  );
};

export default Feedback;
