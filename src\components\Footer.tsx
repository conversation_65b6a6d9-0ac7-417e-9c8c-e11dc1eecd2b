
import React from 'react';
import { NavLink } from 'react-router-dom';
import { Mail, Facebook, Instagram, Youtube, Github, Linkedin, MessageCircle } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-black border-t border-green-500/10">
      <div className="max-w-7xl mx-auto px-6 md:px-12 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
          {/* Logo and Description */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 relative">
                <img 
                  src="/images/1747757610488-photoaidcom-cropped.png" 
                  alt="Psycotik Crew Logo" 
                  className="h-full w-full object-contain" 
                />
              </div>
              <h2 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-green-400 to-green-500">
                Tamilselvan S
              </h2>
            </div>
            <p className="text-gray-300 max-w-md">
             Innovative technology professional specializing in Ethical Hacking, Application Development, and Cybersecurity. A prolific creator with a passion for developing secure and cutting-edge solutions that push the boundaries of technology.
            </p>
           <div className="flex space-x-4 pt-2">
  <a 
    href=" https://www.instagram.com/p/DL7imlFvUHk/?igsh=MXJjbG9scng0dnZrZA%3D%3D" 
    target="_blank" 
    rel="noopener noreferrer"
    className="text-gray-400 hover:text-green-400 transition-colors"
  >
    <Instagram size={20} />
  </a>
  <a 
    href="https://www.linkedin.com/company/cyberwolf-team/" 
    target="_blank" 
    rel="noopener noreferrer"
    className="text-gray-400 hover:text-green-400 transition-colors"
  >
    <Linkedin size={20} />
  </a>
  <a
    href="https://github.com/Tamilselvan-S-Cyber-Security"
    target="_blank"
    rel="noopener noreferrer"
    className="text-gray-400 hover:text-green-400 transition-colors"
  >
    <Github size={20} />
  </a>
  <a
    href="https://whatsapp.com/channel/0029VbAmBt7GufIqh9iwND05"
    target="_blank"
    rel="noopener noreferrer"
    className="text-gray-400 hover:text-green-400 transition-colors"
    title="Join our WhatsApp Community"
  >
    <MessageCircle size={20} />
  </a>
</div>

          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-white font-medium mb-4 pb-2 border-b border-green-500/10">
              Quick Links
            </h3>
            <ul className="space-y-2">
              <li>
                <NavLink 
                  to="/" 
                  className="text-gray-300 hover:text-green-400 transition-colors"
                >
                  Home
                </NavLink>
              </li>
              <li>
                <NavLink 
                  to="/services" 
                  className="text-gray-300 hover:text-green-400 transition-colors"
                >
                  Join Course
                </NavLink>
              </li>
              <li>
                <NavLink 
                  to="/blog" 
                  className="text-gray-300 hover:text-green-400 transition-colors"
                >
                  Blog
                </NavLink>
              </li>
              <li>
                <NavLink
                  to="/references"
                  className="text-gray-300 hover:text-green-400 transition-colors"
                >
                  References
                </NavLink>
              </li>
               <li>
                <NavLink
                  to="/roadmap"
                  className="text-gray-300 hover:text-green-400 transition-colors"
                >
                  Course Roadmap
                </NavLink>
              </li>
              <li>
                <NavLink
                  to="/events"
                  className="text-gray-300 hover:text-green-400 transition-colors"
                >
                  Upcoming Events
                </NavLink>
              </li>
              <li>
                <NavLink
                  to="/linux-commands"
                  className="text-gray-300 hover:text-green-400 transition-colors"
                >
                  Linux Commands 
                </NavLink>
              </li>
              <li>
                <NavLink
                  to="/terminal"
                  className="text-gray-300 hover:text-green-400 transition-colors"
                >
                  Linux Terminal
                </NavLink>
              </li>
              <li>
                <NavLink
                  to="/author"
                  className="text-gray-300 hover:text-green-400 transition-colors"
                >
                 Tools & Authors
                </NavLink>
              </li>
              <li>
                <NavLink
                  to="/community-support"
                  className="text-gray-300 hover:text-green-400 transition-colors"
                >
                  Community Support
                </NavLink>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-white font-medium mb-4 pb-2 border-b border-green-500/10">
              Contact Us
            </h3>
            <ul className="space-y-4">
              <li className="flex items-center space-x-3 text-gray-300">
                <Mail size={16} className="text-green-500" />
                <span><EMAIL></span>
              </li>
  
                <a
                href="/community-support"
                className="bg-transparent border-2 border-psyco-green-DEFAULT text-psyco-green-DEFAULT hover:bg-psyco-green-DEFAULT hover:text-white font-bold py-4 px-8 rounded-lg transition-all duration-300 flex items-center justify-center text-lg"
              >
                 Community Support
               
              </a>
            </ul>
          </div>
        </div>

        <div className="border-t border-green-500/10 mt-12 pt-6 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">
            © {new Date().getFullYear()} Cyber Wolf. All rights reserved.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <NavLink
              to="/privacy-policy"
              className="text-gray-400 hover:text-green-400 text-sm transition-colors"
            >
              Privacy Policy
            </NavLink>
            <NavLink
              to="/terms-of-service"
              className="text-gray-400 hover:text-green-400 text-sm transition-colors"
            >
              Terms of Service
            </NavLink>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
