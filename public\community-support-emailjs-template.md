# Community Support EmailJS Template Setup

## EmailJS Configuration

**Service ID:** `service_toqtpup`
**Template ID:** `template_mz6tbzt`
**Public Key:** `DcOjG5IliQPCFC9kV`

## Template Variables

The following variables are available in your EmailJS template:

### Basic Information
- `{{to_email}}` - Recipient email (<EMAIL>)
- `{{from_name}}` - User's name
- `{{from_email}}` - User's email address
- `{{subject}}` - Email subject line

### User Details
- `{{user_name}}` - Full name of the user
- `{{user_email}}` - User's email address
- `{{user_phone}}` - User's phone number

### Support Request Details
- `{{support_subject}}` - Subject of the support request
- `{{support_category}}` - Category (Technical, Community, Course, General)
- `{{support_priority}}` - Priority level (LOW, MEDIUM, HIGH, URGENT)
- `{{support_message}}` - The detailed message from the user

### Metadata
- `{{submission_time}}` - When the request was submitted
- `{{message}}` - Complete formatted message with all details

## HTML Template Usage

You can use the provided HTML template (`public/community-support.html`) as your EmailJS template by:

1. Opening the HTML file
2. Copying the entire content
3. Pasting it into your EmailJS template editor
4. Making sure all variables are properly mapped

## Template Structure

The template includes:
- **Header Section**: Cyber Wolf branding with logo
- **Support Request Information**: Grid layout with all user and request details
- **Priority Badges**: Visual indicators for request priority
- **Message Section**: Formatted display of the user's message
- **Response Commitment**: 24-hour response promise
- **Footer**: Contact information and branding

## Styling Features

- **Responsive Design**: Works on all devices
- **Glassmorphism Effects**: Modern UI with backdrop blur
- **Priority Color Coding**: Different colors for different priority levels
- **Professional Layout**: Clean, organized information display
- **Brand Consistency**: Matches Cyber Wolf design system

## Priority Badge Classes

- `.priority-low` - Green background for low priority
- `.priority-medium` - Yellow background for medium priority  
- `.priority-high` - Orange background for high priority
- `.priority-urgent` - Red background with pulse animation for urgent

## Setup Instructions

1. **Create EmailJS Account**: Sign up at emailjs.com
2. **Add Email Service**: Configure your email provider
3. **Create Template**: Use the provided HTML template
4. **Configure Variables**: Map all the template variables
5. **Test**: Send a test email to verify everything works
6. **Update Credentials**: Make sure the service ID, template ID, and public key match

## Testing

To test the community support form:
1. Navigate to `/community-support` on your website
2. Fill out the form with test data
3. Submit the form
4. Check your email for the formatted support request

## Troubleshooting

**Common Issues:**
- **Variables not showing**: Check variable names match exactly
- **Styling broken**: Ensure HTML template is copied completely
- **Emails not sending**: Verify EmailJS credentials are correct
- **Form validation errors**: Check required fields are filled

**Support:**
- EmailJS Documentation: https://www.emailjs.com/docs/
- Cyber Wolf Support: <EMAIL>

---

**© 2024 Cyber Wolf - Advanced Cybersecurity Training Platform**
