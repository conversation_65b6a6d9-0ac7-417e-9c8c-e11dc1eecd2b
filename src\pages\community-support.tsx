import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import emailjs from '@emailjs/browser';
import { 
  Users, 
  MessageCircle, 
  Heart, 
  Shield, 
  Send, 
  CheckCircle, 
  AlertCircle,
  Mail,
  Phone,
  MapPin,
  Clock,
  Star,
  ArrowRight,
  ExternalLink
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

// Form validation schema
const supportFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(10, 'Phone number must be at least 10 digits'),
  subject: z.string().min(5, 'Subject must be at least 5 characters'),
  category: z.enum(['technical', 'community', 'course', 'general'], {
    required_error: 'Please select a support category',
  }),
  priority: z.enum(['low', 'medium', 'high', 'urgent'], {
    required_error: 'Please select priority level',
  }),
  message: z.string().min(20, 'Message must be at least 20 characters'),
  agreeToTerms: z.boolean().refine(val => val === true, {
    message: 'You must agree to our terms and conditions',
  }),
});

type SupportFormData = z.infer<typeof supportFormSchema>;

const CommunitySupport = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const { toast } = useToast();

  // EmailJS Configuration
  const EMAILJS_SERVICE_ID = 'service_toqtpup';
  const EMAILJS_TEMPLATE_ID = 'template_mz6tbzt';
  const EMAILJS_PUBLIC_KEY = 'DcOjG5IliQPCFC9kV';

  // Initialize EmailJS
  useEffect(() => {
    emailjs.init(EMAILJS_PUBLIC_KEY);
    window.scrollTo(0, 0);
  }, []);

  const form = useForm<SupportFormData>({
    resolver: zodResolver(supportFormSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      subject: '',
      category: undefined,
      priority: undefined,
      message: '',
      agreeToTerms: false,
    },
  });

  const onSubmit = async (data: SupportFormData) => {
    setIsSubmitting(true);

    try {
      // Prepare EmailJS template parameters
      const templateParams = {
        to_email: '<EMAIL>',
        from_name: data.name,
        from_email: data.email,
        subject: `Community Support Request - ${data.subject}`,
        user_name: data.name,
        user_email: data.email,
        user_phone: data.phone,
        support_subject: data.subject,
        support_category: data.category.charAt(0).toUpperCase() + data.category.slice(1),
        support_priority: data.priority.toUpperCase(),
        support_message: data.message,
        submission_time: new Date().toLocaleString(),
        message: `
New Community Support Request

Name: ${data.name}
Email: ${data.email}
Phone: ${data.phone}
Subject: ${data.subject}
Category: ${data.category.charAt(0).toUpperCase() + data.category.slice(1)}
Priority: ${data.priority.toUpperCase()}

Message:
${data.message}

Submitted on: ${new Date().toLocaleString()}
        `
      };

      // Send email using EmailJS
      const result = await emailjs.send(
        EMAILJS_SERVICE_ID,
        EMAILJS_TEMPLATE_ID,
        templateParams
      );

      console.log('Support request sent successfully:', result);

      // Show success state
      setIsSubmitted(true);

      toast({
        title: "Support Request Submitted!",
        description: "Thank you for reaching out. Our team will get back to you within 24 hours.",
      });

      // Auto scroll to top
      window.scrollTo({ top: 0, behavior: 'smooth' });

    } catch (error) {
      console.error('Error sending support request:', error);
      toast({
        title: "Error",
        description: "Something went wrong. Please try again or contact us directly.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-psyco-black-DEFAULT via-psyco-black-light to-psyco-black-DEFAULT flex items-center justify-center px-6">
        <div className="max-w-2xl mx-auto text-center">
          <div className="glassmorphism p-12 rounded-2xl">
            <div className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="h-10 w-10 text-white" />
            </div>
            <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Request Submitted Successfully!
            </h1>
            <p className="text-xl text-gray-300 mb-8">
              Thank you for reaching out to our community support team. We've received your request and will respond within 24 hours.
            </p>
            <div className="space-y-4">
              <button
                onClick={() => {
                  setIsSubmitted(false);
                  form.reset();
                }}
                className="bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-bold py-3 px-8 rounded-lg transition-all duration-300 mr-4"
              >
                Submit Another Request
              </button>
              <a
                href="/"
                className="bg-transparent border-2 border-psyco-green-DEFAULT text-psyco-green-DEFAULT hover:bg-psyco-green-DEFAULT hover:text-white font-bold py-3 px-8 rounded-lg transition-all duration-300 inline-block"
              >
                Back to Home
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-psyco-black-DEFAULT via-psyco-black-light to-psyco-black-DEFAULT">
      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 px-6 md:px-12">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute w-96 h-96 bg-psyco-green-DEFAULT/10 rounded-full blur-3xl top-1/4 left-1/4 animate-pulse"></div>
          <div className="absolute w-96 h-96 bg-psyco-green-DEFAULT/5 rounded-full blur-3xl bottom-1/4 right-1/4 animate-pulse"></div>
        </div>
        
        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center mb-16">
            <div className="flex justify-center mb-8">
              <div className="w-20 h-20 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center">
                <Users className="h-10 w-10 text-white" />
              </div>
            </div>
            
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 animate-fade-in">
              COMMUNITY 
              <span className="block text-psyco-green-DEFAULT">SUPPORT</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-300 mb-8 animate-fade-in max-w-4xl mx-auto">
              Get help from our dedicated support team and connect with the Cyber Wolf community. 
              We're here to assist you on your cybersecurity journey.
            </p>
          </div>
        </div>
      </section>

      {/* Support Categories Section */}
      <section className="py-16 px-6 md:px-12 bg-psyco-black-light">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">How Can We Help You?</h2>
            <p className="text-lg text-gray-400">Choose from our support categories</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                icon: <Shield size={32} />,
                title: "Technical Support",
                description: "Get help with tools, software, and technical issues",
                color: "from-blue-500 to-blue-600"
              },
              {
                icon: <Users size={32} />,
                title: "Community Help",
                description: "Connect with fellow learners and get community support",
                color: "from-green-500 to-green-600"
              },
              {
                icon: <MessageCircle size={32} />,
                title: "Course Support",
                description: "Questions about courses, content, and learning materials",
                color: "from-purple-500 to-purple-600"
              },
              {
                icon: <Heart size={32} />,
                title: "General Inquiry",
                description: "Any other questions or feedback you'd like to share",
                color: "from-pink-500 to-pink-600"
              }
            ].map((category, index) => (
              <div 
                key={index}
                className="glassmorphism p-6 rounded-xl text-center hover:scale-105 transition-transform duration-300"
              >
                <div className={`w-16 h-16 bg-gradient-to-r ${category.color} rounded-full flex items-center justify-center mx-auto mb-4`}>
                  <div className="text-white">
                    {category.icon}
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-3">{category.title}</h3>
                <p className="text-gray-400 text-sm">{category.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Support Form Section */}
      <section className="py-20 px-6 md:px-12">
        <div className="max-w-4xl mx-auto">
          <div className="glassmorphism p-8 md:p-12 rounded-2xl">
            <div className="text-center mb-8">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Submit Support Request</h2>
              <p className="text-lg text-gray-400">Fill out the form below and we'll get back to you within 24 hours</p>
            </div>

            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Personal Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-white font-medium mb-2">Full Name *</label>
                  <input
                    {...form.register('name')}
                    type="text"
                    className="w-full px-4 py-3 bg-psyco-black-light border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-psyco-green-DEFAULT focus:outline-none transition-colors"
                    placeholder="Enter your full name"
                  />
                  {form.formState.errors.name && (
                    <p className="text-red-400 text-sm mt-1">{form.formState.errors.name.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-white font-medium mb-2">Email Address *</label>
                  <input
                    {...form.register('email')}
                    type="email"
                    className="w-full px-4 py-3 bg-psyco-black-light border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-psyco-green-DEFAULT focus:outline-none transition-colors"
                    placeholder="Enter your email address"
                  />
                  {form.formState.errors.email && (
                    <p className="text-red-400 text-sm mt-1">{form.formState.errors.email.message}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-white font-medium mb-2">Phone Number *</label>
                  <input
                    {...form.register('phone')}
                    type="tel"
                    className="w-full px-4 py-3 bg-psyco-black-light border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-psyco-green-DEFAULT focus:outline-none transition-colors"
                    placeholder="Enter your phone number"
                  />
                  {form.formState.errors.phone && (
                    <p className="text-red-400 text-sm mt-1">{form.formState.errors.phone.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-white font-medium mb-2">Subject *</label>
                  <input
                    {...form.register('subject')}
                    type="text"
                    className="w-full px-4 py-3 bg-psyco-black-light border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-psyco-green-DEFAULT focus:outline-none transition-colors"
                    placeholder="Brief subject of your request"
                  />
                  {form.formState.errors.subject && (
                    <p className="text-red-400 text-sm mt-1">{form.formState.errors.subject.message}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-white font-medium mb-2">Support Category *</label>
                  <select
                    {...form.register('category')}
                    className="w-full px-4 py-3 bg-psyco-black-light border border-gray-600 rounded-lg text-white focus:border-psyco-green-DEFAULT focus:outline-none transition-colors"
                  >
                    <option value="">Select a category</option>
                    <option value="technical">Technical Support</option>
                    <option value="community">Community Help</option>
                    <option value="course">Course Support</option>
                    <option value="general">General Inquiry</option>
                  </select>
                  {form.formState.errors.category && (
                    <p className="text-red-400 text-sm mt-1">{form.formState.errors.category.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-white font-medium mb-2">Priority Level *</label>
                  <select
                    {...form.register('priority')}
                    className="w-full px-4 py-3 bg-psyco-black-light border border-gray-600 rounded-lg text-white focus:border-psyco-green-DEFAULT focus:outline-none transition-colors"
                  >
                    <option value="">Select priority</option>
                    <option value="low">Low - General question</option>
                    <option value="medium">Medium - Need assistance</option>
                    <option value="high">High - Important issue</option>
                    <option value="urgent">Urgent - Critical problem</option>
                  </select>
                  {form.formState.errors.priority && (
                    <p className="text-red-400 text-sm mt-1">{form.formState.errors.priority.message}</p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-white font-medium mb-2">Message *</label>
                <textarea
                  {...form.register('message')}
                  rows={6}
                  className="w-full px-4 py-3 bg-psyco-black-light border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-psyco-green-DEFAULT focus:outline-none transition-colors resize-vertical"
                  placeholder="Please describe your issue or question in detail..."
                />
                {form.formState.errors.message && (
                  <p className="text-red-400 text-sm mt-1">{form.formState.errors.message.message}</p>
                )}
              </div>

              <div className="flex items-start space-x-3">
                <input
                  {...form.register('agreeToTerms')}
                  type="checkbox"
                  className="mt-1 h-4 w-4 text-psyco-green-DEFAULT bg-psyco-black-light border-gray-600 rounded focus:ring-psyco-green-DEFAULT focus:ring-2"
                />
                <label className="text-gray-300 text-sm">
                  I agree to the{' '}
                  <a href="/terms-of-service" className="text-psyco-green-DEFAULT hover:underline">
                    Terms of Service
                  </a>{' '}
                  and{' '}
                  <a href="/privacy-policy" className="text-psyco-green-DEFAULT hover:underline">
                    Privacy Policy
                  </a>
                  *
                </label>
              </div>
              {form.formState.errors.agreeToTerms && (
                <p className="text-red-400 text-sm">{form.formState.errors.agreeToTerms.message}</p>
              )}

              <div className="text-center pt-6">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-bold py-4 px-12 rounded-lg transition-all duration-300 flex items-center justify-center mx-auto disabled:opacity-50 disabled:cursor-not-allowed btn-glow"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                      Submitting...
                    </>
                  ) : (
                    <>
                      Submit Request
                      <Send className="ml-2 h-5 w-5" />
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </section>

      {/* Contact Information Section */}
      <section className="py-16 px-6 md:px-12 bg-psyco-black-light">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Other Ways to Reach Us</h2>
            <p className="text-lg text-gray-400">Multiple channels to get the support you need</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="glassmorphism p-8 rounded-xl text-center">
              <div className="w-16 h-16 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center mx-auto mb-4">
                <Mail className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-3">Email Support</h3>
              <p className="text-gray-400 mb-4">Get detailed help via email</p>
              <a 
                href="mailto:<EMAIL>"
                className="text-psyco-green-DEFAULT hover:text-white transition-colors font-medium"
              >
                <EMAIL>
              </a>
            </div>

            <div className="glassmorphism p-8 rounded-xl text-center">
              <div className="w-16 h-16 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center mx-auto mb-4">
                <MessageCircle className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-3">WhatsApp Community</h3>
              <p className="text-gray-400 mb-4">Join our active community</p>
              <a 
                href="https://whatsapp.com/channel/0029VbAmBt7GufIqh9iwND05"
                target="_blank"
                rel="noopener noreferrer"
                className="text-psyco-green-DEFAULT hover:text-white transition-colors font-medium inline-flex items-center"
              >
                Join Community
                <ExternalLink className="ml-1 h-4 w-4" />
              </a>
            </div>

            <div className="glassmorphism p-8 rounded-xl text-center">
              <div className="w-16 h-16 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center mx-auto mb-4">
                <Phone className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-3">Phone Support</h3>
              <p className="text-gray-400 mb-4">Direct phone assistance</p>
              <div className="space-y-2">
                <a 
                  href="tel:+916374344424"
                  className="block text-psyco-green-DEFAULT hover:text-white transition-colors font-medium"
                >
                  +91 6374 344 424
                </a>
                <a 
                  href="tel:+916379869678"
                  className="block text-psyco-green-DEFAULT hover:text-white transition-colors font-medium"
                >
                  +91 6379 869 678
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-6 md:px-12">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Frequently Asked Questions</h2>
            <p className="text-lg text-gray-400">Quick answers to common questions</p>
          </div>
          
          <div className="space-y-6">
            {[
              {
                question: "How quickly will I receive a response?",
                answer: "We aim to respond to all support requests within 24 hours. Urgent issues are prioritized and typically receive responses within 4-6 hours."
              },
              {
                question: "What information should I include in my support request?",
                answer: "Please provide as much detail as possible about your issue, including any error messages, steps you've taken, and your system information if relevant."
              },
              {
                question: "Can I get help with course content?",
                answer: "Absolutely! Our team can help with course materials, assignments, technical setup, and any learning-related questions you may have."
              },
              {
                question: "Is community support available 24/7?",
                answer: "While our formal support team operates during business hours, our WhatsApp community is active 24/7 with peer-to-peer support from fellow learners."
              }
            ].map((faq, index) => (
              <div key={index} className="glassmorphism p-6 rounded-xl">
                <h3 className="text-xl font-bold text-white mb-3">{faq.question}</h3>
                <p className="text-gray-400">{faq.answer}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default CommunitySupport;
