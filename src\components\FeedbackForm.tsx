import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import emailjs from '@emailjs/browser';
import { 
  MessageSquare, 
  Star, 
  Send, 
  CheckCircle, 
  AlertCircle,
  ThumbsUp,
  ThumbsDown,
  Heart,
  Lightbulb,
  Bug,
  Smile,
  Meh,
  Frown
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

// Form validation schema
const feedbackFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  feedbackType: z.enum(['general', 'course', 'website', 'suggestion', 'bug', 'compliment'], {
    required_error: 'Please select a feedback type',
  }),
  rating: z.number().min(1).max(5, 'Please provide a rating between 1-5'),
  subject: z.string().min(5, 'Subject must be at least 5 characters'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
  wouldRecommend: z.boolean(),
  allowContact: z.boolean(),
});

type FeedbackFormData = z.infer<typeof feedbackFormSchema>;

interface FeedbackFormProps {
  isOpen: boolean;
  onClose: () => void;
}

const FeedbackForm: React.FC<FeedbackFormProps> = ({ isOpen, onClose }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const { toast } = useToast();

  // EmailJS Configuration
  const EMAILJS_SERVICE_ID = 'service_toqtpup';
  const EMAILJS_TEMPLATE_ID = 'template_pjsy8lo'; // You'll need to create this template
  const EMAILJS_PUBLIC_KEY = 'DcOjG5IliQPCFC9kV';

  // Initialize EmailJS
  useEffect(() => {
    console.log('Initializing EmailJS with public key:', EMAILJS_PUBLIC_KEY);
    emailjs.init(EMAILJS_PUBLIC_KEY);
  }, []);

  const form = useForm<FeedbackFormData>({
    resolver: zodResolver(feedbackFormSchema),
    mode: 'onChange', // Enable real-time validation
    defaultValues: {
      name: '',
      email: '',
      feedbackType: 'general' as const, // Set default feedback type
      rating: 5,
      subject: '',
      message: '',
      wouldRecommend: true,
      allowContact: false,
    },
  });

  // Debug form state changes
  useEffect(() => {
    console.log('Form state changed:', {
      isValid: form.formState.isValid,
      errors: form.formState.errors,
      dirtyFields: form.formState.dirtyFields,
      values: form.getValues()
    });
  }, [form.formState.isValid, form.formState.errors, form.formState.dirtyFields, form]);

  const onSubmit = async (data: FeedbackFormData) => {
    console.log('Form submitted with data:', data);
    console.log('Current form values:', form.getValues());
    setIsSubmitting(true);

    try {
      // Get the latest form values to ensure we have the most current data
      const currentValues = form.getValues();
      const finalData = { ...data, ...currentValues };

      console.log('Final data being sent:', finalData);

      // Validate required fields
      if (!finalData.name || !finalData.email || !finalData.feedbackType || !finalData.rating || !finalData.subject || !finalData.message) {
        console.error('Missing required fields:', {
          name: !!finalData.name,
          email: !!finalData.email,
          feedbackType: !!finalData.feedbackType,
          rating: !!finalData.rating,
          subject: !!finalData.subject,
          message: !!finalData.message
        });
        throw new Error('Please fill in all required fields');
      }

      // Prepare EmailJS template parameters
      const templateParams = {
        to_email: '<EMAIL>',
        from_name: finalData.name,
        from_email: finalData.email,
        subject: `Feedback: ${finalData.subject}`,
        user_name: finalData.name,
        user_email: finalData.email,
        feedback_type: finalData.feedbackType.charAt(0).toUpperCase() + finalData.feedbackType.slice(1),
        feedback_rating: finalData.rating,
        feedback_subject: finalData.subject,
        feedback_message: finalData.message,
        would_recommend: finalData.wouldRecommend ? 'Yes' : 'No',
        allow_contact: finalData.allowContact ? 'Yes' : 'No',
        submission_time: new Date().toLocaleString(),
        rating_stars: '★'.repeat(finalData.rating) + '☆'.repeat(5 - finalData.rating),
        message: `
New Feedback Received

Name: ${finalData.name}
Email: ${finalData.email}
Type: ${finalData.feedbackType.charAt(0).toUpperCase() + finalData.feedbackType.slice(1)}
Rating: ${finalData.rating}/5 stars
Subject: ${finalData.subject}

Would Recommend: ${finalData.wouldRecommend ? 'Yes' : 'No'}
Allow Contact: ${finalData.allowContact ? 'Yes' : 'No'}

Message:
${finalData.message}

Submitted on: ${new Date().toLocaleString()}
        `
      };

      console.log('Sending email with params:', templateParams);

      // Send email using EmailJS
      const result = await emailjs.send(
        EMAILJS_SERVICE_ID,
        EMAILJS_TEMPLATE_ID,
        templateParams
      );

      console.log('Feedback sent successfully:', result);

      // Show success state
      setIsSubmitted(true);

      toast({
        title: "Feedback Submitted!",
        description: "Thank you for your valuable feedback. We appreciate your input!",
      });

    } catch (error) {
      console.error('Error sending feedback:', error);

      let errorMessage = "Something went wrong. Please try again later.";
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setIsSubmitted(false);
    form.reset({
      name: '',
      email: '',
      feedbackType: 'general' as const,
      rating: 5,
      subject: '',
      message: '',
      wouldRecommend: true,
      allowContact: false,
    });
    onClose();
  };

  if (!isOpen) return null;

  if (isSubmitted) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-gradient-to-br from-psyco-black-DEFAULT to-psyco-black-light rounded-2xl p-8 max-w-md w-full text-center glassmorphism">
          <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="h-8 w-8 text-white" />
          </div>
          <h3 className="text-2xl font-bold text-white mb-3">Thank You!</h3>
          <p className="text-gray-300 mb-6">
            Your feedback has been submitted successfully. We value your input and will use it to improve our services.
          </p>
          <button
            onClick={handleClose}
            className="bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-bold py-3 px-6 rounded-lg transition-all duration-300"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  const feedbackTypes = [
    { value: 'general', label: 'General Feedback', icon: MessageSquare },
    { value: 'course', label: 'Course Content', icon: Star },
    { value: 'website', label: 'Website Experience', icon: ThumbsUp },
    { value: 'suggestion', label: 'Suggestion', icon: Lightbulb },
    { value: 'bug', label: 'Report Bug', icon: Bug },
    { value: 'compliment', label: 'Compliment', icon: Heart },
  ];

  const ratingEmojis = [
    { value: 1, emoji: '😞', label: 'Very Poor' },
    { value: 2, emoji: '😕', label: 'Poor' },
    { value: 3, emoji: '😐', label: 'Average' },
    { value: 4, emoji: '😊', label: 'Good' },
    { value: 5, emoji: '😍', label: 'Excellent' },
  ];

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-2 sm:p-4">
      <div className="bg-gradient-to-br from-psyco-black-DEFAULT to-psyco-black-light rounded-2xl max-w-2xl w-full max-h-[95vh] sm:max-h-[90vh] overflow-y-auto glassmorphism shadow-2xl">
        {/* Header */}
        <div className="p-6 border-b border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center">
                <MessageSquare className="h-5 w-5 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">Share Your Feedback</h2>
                <p className="text-gray-400">Help us improve your experience</p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-700 rounded-lg"
            >
              ✕
            </button>
          </div>

          {/* Progress indicator */}
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-psyco-green-DEFAULT h-2 rounded-full transition-all duration-300"
              style={{
                width: `${Math.min(100, (Object.keys(form.formState.dirtyFields).length / 6) * 100)}%`
              }}
            ></div>
          </div>
          <p className="text-xs text-gray-400 mt-2 text-center">
            {Object.keys(form.formState.dirtyFields).length} of 6 required fields completed
          </p>
        </div>

        {/* Form */}
        <form onSubmit={form.handleSubmit(onSubmit)} className="p-6 space-y-6">
          {/* Personal Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-white font-medium mb-2">Name *</label>
              <input
                {...form.register('name')}
                type="text"
                className="w-full px-4 py-3 bg-psyco-black-light border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-psyco-green-DEFAULT focus:outline-none transition-colors"
                placeholder="Your name"
              />
              {form.formState.errors.name && (
                <p className="text-red-400 text-sm mt-1">{form.formState.errors.name.message}</p>
              )}
            </div>

            <div>
              <label className="block text-white font-medium mb-2">Email *</label>
              <input
                {...form.register('email')}
                type="email"
                className="w-full px-4 py-3 bg-psyco-black-light border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-psyco-green-DEFAULT focus:outline-none transition-colors"
                placeholder="<EMAIL>"
              />
              {form.formState.errors.email && (
                <p className="text-red-400 text-sm mt-1">{form.formState.errors.email.message}</p>
              )}
            </div>
          </div>

          {/* Feedback Type */}
          <div>
            <label className="block text-white font-medium mb-3">
              Feedback Type *
              <span className="text-sm text-psyco-green-DEFAULT ml-2">
                (Selected: {form.watch('feedbackType')?.charAt(0).toUpperCase() + form.watch('feedbackType')?.slice(1) || 'None'})
              </span>
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {feedbackTypes.map((type) => {
                const IconComponent = type.icon;
                const isSelected = form.watch('feedbackType') === type.value;
                return (
                  <div
                    key={type.value}
                    className={`relative flex items-center space-x-2 p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 group hover:scale-105 select-none ${
                      isSelected
                        ? 'border-psyco-green-DEFAULT bg-psyco-green-DEFAULT/20 shadow-lg shadow-psyco-green-DEFAULT/25'
                        : 'border-gray-600 hover:border-psyco-green-DEFAULT/50 hover:bg-psyco-green-DEFAULT/5'
                    }`}
                    onClick={() => {
                      console.log('Feedback type clicked:', type.value);
                      form.setValue('feedbackType', type.value as any, {
                        shouldValidate: true,
                        shouldDirty: true,
                        shouldTouch: true
                      });
                    }}
                    onTouchStart={(e) => {
                      e.preventDefault();
                      console.log('Feedback type touched:', type.value);
                      form.setValue('feedbackType', type.value as any, {
                        shouldValidate: true,
                        shouldDirty: true,
                        shouldTouch: true
                      });
                    }}
                  >
                    <input
                      {...form.register('feedbackType')}
                      type="radio"
                      value={type.value}
                      className="sr-only"
                      tabIndex={-1}
                    />
                    <div className={`p-2 rounded-lg transition-all duration-200 pointer-events-none ${
                      isSelected
                        ? 'bg-psyco-green-DEFAULT text-white'
                        : 'bg-gray-700 text-psyco-green-DEFAULT group-hover:bg-psyco-green-DEFAULT/20'
                    }`}>
                      <IconComponent className="h-4 w-4" />
                    </div>
                    <span className={`text-sm font-medium transition-colors pointer-events-none ${
                      isSelected ? 'text-white' : 'text-gray-300 group-hover:text-white'
                    }`}>
                      {type.label}
                    </span>

                    {/* Selection indicator */}
                    {isSelected && (
                      <div className="absolute -top-1 -right-1 w-4 h-4 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center pointer-events-none">
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
            {form.formState.errors.feedbackType && (
              <p className="text-red-400 text-sm mt-1 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {form.formState.errors.feedbackType.message}
              </p>
            )}
          </div>

          {/* Rating */}
          <div>
            <label className="block text-white font-medium mb-3">
              Overall Rating *
              <span className="text-sm text-psyco-green-DEFAULT ml-2">
                (Selected: {form.watch('rating')}/5 - {'★'.repeat(form.watch('rating') || 0)}{'☆'.repeat(5 - (form.watch('rating') || 0))})
              </span>
            </label>
            <div className="flex items-center justify-center space-x-2 md:space-x-3 flex-wrap gap-2">
              {ratingEmojis.map((rating) => {
                const isSelected = form.watch('rating') === rating.value;
                return (
                  <div
                    key={rating.value}
                    className={`relative flex flex-col items-center p-3 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:scale-105 min-w-[70px] select-none ${
                      isSelected
                        ? 'border-psyco-green-DEFAULT bg-psyco-green-DEFAULT/20 shadow-lg shadow-psyco-green-DEFAULT/25 scale-105'
                        : 'border-gray-600 hover:border-psyco-green-DEFAULT/50 hover:bg-psyco-green-DEFAULT/5'
                    }`}
                    onClick={() => {
                      console.log('Rating clicked:', rating.value);
                      form.setValue('rating', rating.value, {
                        shouldValidate: true,
                        shouldDirty: true,
                        shouldTouch: true
                      });
                    }}
                    onTouchStart={(e) => {
                      e.preventDefault();
                      console.log('Rating touched:', rating.value);
                      form.setValue('rating', rating.value, {
                        shouldValidate: true,
                        shouldDirty: true,
                        shouldTouch: true
                      });
                    }}
                  >
                    <input
                      {...form.register('rating', { valueAsNumber: true })}
                      type="radio"
                      value={rating.value}
                      className="sr-only"
                      tabIndex={-1}
                    />
                    <span className={`text-3xl mb-2 transition-transform duration-200 pointer-events-none ${
                      isSelected ? 'scale-110' : ''
                    }`}>
                      {rating.emoji}
                    </span>
                    <span className={`text-xs font-medium text-center transition-colors pointer-events-none ${
                      isSelected ? 'text-white' : 'text-gray-400'
                    }`}>
                      {rating.label}
                    </span>

                    {/* Star indicator */}
                    <div className={`flex mt-1 transition-opacity duration-200 pointer-events-none ${
                      isSelected ? 'opacity-100' : 'opacity-0'
                    }`}>
                      {[...Array(rating.value)].map((_, i) => (
                        <Star key={i} className="h-2 w-2 text-yellow-400 fill-current" />
                      ))}
                    </div>

                    {/* Selection indicator */}
                    {isSelected && (
                      <div className="absolute -top-1 -right-1 w-4 h-4 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center pointer-events-none">
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
            {form.formState.errors.rating && (
              <p className="text-red-400 text-sm mt-2 flex items-center justify-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {form.formState.errors.rating.message}
              </p>
            )}
          </div>

          {/* Subject */}
          <div>
            <label className="block text-white font-medium mb-2">Subject *</label>
            <input
              {...form.register('subject')}
              type="text"
              className="w-full px-4 py-3 bg-psyco-black-light border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-psyco-green-DEFAULT focus:outline-none transition-colors"
              placeholder="Brief subject of your feedback"
            />
            {form.formState.errors.subject && (
              <p className="text-red-400 text-sm mt-1">{form.formState.errors.subject.message}</p>
            )}
          </div>

          {/* Message */}
          <div>
            <label className="block text-white font-medium mb-2">Your Feedback *</label>
            <textarea
              {...form.register('message')}
              rows={4}
              className="w-full px-4 py-3 bg-psyco-black-light border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-psyco-green-DEFAULT focus:outline-none transition-colors resize-vertical"
              placeholder="Please share your detailed feedback..."
            />
            {form.formState.errors.message && (
              <p className="text-red-400 text-sm mt-1">{form.formState.errors.message.message}</p>
            )}
          </div>

          {/* Additional Options */}
          <div className="space-y-3">
            <label className="flex items-center space-x-3">
              <input
                {...form.register('wouldRecommend')}
                type="checkbox"
                className="h-4 w-4 text-psyco-green-DEFAULT bg-psyco-black-light border-gray-600 rounded focus:ring-psyco-green-DEFAULT focus:ring-2"
              />
              <span className="text-white">I would recommend Cyber Wolf to others</span>
            </label>

            <label className="flex items-center space-x-3">
              <input
                {...form.register('allowContact')}
                type="checkbox"
                className="h-4 w-4 text-psyco-green-DEFAULT bg-psyco-black-light border-gray-600 rounded focus:ring-psyco-green-DEFAULT focus:ring-2"
              />
              <span className="text-white">You may contact me about this feedback</span>
            </label>
          </div>

          {/* Submit Button */}
          <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-4 pt-6 border-t border-gray-700">
            

            <button
              type="button"
              onClick={handleClose}
              className="px-6 py-3 border-2 border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 hover:border-gray-500 transition-all duration-300 font-medium"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className={`relative overflow-hidden bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-bold py-3 px-8 rounded-lg transition-all duration-300 flex items-center justify-center min-w-[160px] ${
                isSubmitting
                  ? 'opacity-50 cursor-not-allowed'
                  : 'hover:scale-105 hover:shadow-lg hover:shadow-psyco-green-DEFAULT/25 btn-glow'
              }`}
              onClick={() => {
                console.log('=== SUBMIT BUTTON CLICKED ===');
                console.log('Form valid:', form.formState.isValid);
                console.log('Form errors:', form.formState.errors);
                console.log('Form values:', form.getValues());
                console.log('Feedback Type:', form.watch('feedbackType'));
                console.log('Rating:', form.watch('rating'));

                // Force validation
                form.trigger();
              }}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-3"></div>
                  <span>Submitting...</span>
                </>
              ) : (
                <>
                  <span>Submit Feedback</span>
                  <Send className="ml-3 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </>
              )}

              {/* Success animation overlay */}
              {!isSubmitting && (
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full hover:translate-x-full transition-transform duration-700"></div>
              )}
            </button>
          </div>

          {/* Form validation summary */}
          {Object.keys(form.formState.errors).length > 0 && (
            <div className="mt-4 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
              <div className="flex items-center text-red-400 text-sm">
                <AlertCircle className="h-4 w-4 mr-2 flex-shrink-0" />
                <span>Please fix the following errors before submitting:</span>
              </div>
              <ul className="mt-2 text-xs text-red-300 space-y-1">
                {Object.entries(form.formState.errors).map(([field, error]) => (
                  <li key={field} className="flex items-center">
                    <span className="w-2 h-2 bg-red-400 rounded-full mr-2 flex-shrink-0"></span>
                    {error?.message}
                  </li>
                ))}
              </ul>
            </div>
          )}


        </form>
      </div>
    </div>
  );
};

export default FeedbackForm;
