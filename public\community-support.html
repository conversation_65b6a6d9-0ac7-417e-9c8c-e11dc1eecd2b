<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Community Support Request - Cyber Wolf</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        
        .header {
            background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 36px;
            position: relative;
            z-index: 1;
        }
        
        .header h1 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .support-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #00ff88;
        }
        
        .support-info h2 {
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .support-info h2::before {
            content: '🎯';
            margin-right: 10px;
            font-size: 1.2em;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .info-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
        }
        
        .info-item h3 {
            color: #00cc6a;
            font-size: 1.1em;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .info-item p {
            color: #555;
            font-size: 1em;
            word-break: break-word;
        }
        
        .priority-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .priority-low { background: #d4edda; color: #155724; }
        .priority-medium { background: #fff3cd; color: #856404; }
        .priority-high { background: #f8d7da; color: #721c24; }
        .priority-urgent { background: #f5c6cb; color: #721c24; animation: pulse 2s infinite; }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .category-badge {
            display: inline-block;
            padding: 6px 12px;
            background: linear-gradient(135deg, #00ff88, #00cc6a);
            color: white;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .message-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            border: 1px solid #e9ecef;
        }
        
        .message-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .message-content {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #00ff88;
            font-size: 1.1em;
            line-height: 1.7;
            color: #444;
            white-space: pre-wrap;
        }
        
        .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .footer h3 {
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .contact-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .contact-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            backdrop-filter: blur(5px);
        }
        
        .contact-item h4 {
            color: #00ff88;
            margin-bottom: 8px;
            font-size: 1.1em;
        }
        
        .contact-item a {
            color: white;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .contact-item a:hover {
            color: #00ff88;
        }
        
        .response-time {
            background: linear-gradient(135deg, #00ff88, #00cc6a);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 25px 0;
            text-align: center;
            font-weight: 600;
        }
        
        .response-time h3 {
            margin-bottom: 10px;
            font-size: 1.4em;
        }
        
        .timestamp {
            background: #e9ecef;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            text-align: center;
            color: #666;
            font-size: 0.95em;
        }
        
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .contact-info {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🐺</div>
            <h1>Community Support Request</h1>
            <p>Cyber Wolf Cybersecurity Training Platform</p>
        </div>
        
        <!-- Content -->
        <div class="content">
            <!-- Support Request Information -->
            <div class="support-info">
                <h2>New Support Request Received</h2>
                
                <div class="info-grid">
                    <div class="info-item">
                        <h3>👤 Name</h3>
                        <p>{{user_name}}</p>
                    </div>
                    
                    <div class="info-item">
                        <h3>📧 Email</h3>
                        <p>{{user_email}}</p>
                    </div>
                    
                    <div class="info-item">
                        <h3>📱 Phone</h3>
                        <p>{{user_phone}}</p>
                    </div>
                    
                    <div class="info-item">
                        <h3>📋 Subject</h3>
                        <p>{{support_subject}}</p>
                    </div>
                    
                    <div class="info-item">
                        <h3>🏷️ Category</h3>
                        <p><span class="category-badge">{{support_category}}</span></p>
                    </div>
                    
                    <div class="info-item">
                        <h3>⚡ Priority</h3>
                        <p><span class="priority-badge priority-{{support_priority}}">{{support_priority}}</span></p>
                    </div>
                </div>
            </div>
            
            <!-- Response Time Promise -->
            <div class="response-time">
                <h3>⏰ Response Commitment</h3>
                <p>We will respond to this request within 24 hours</p>
            </div>
            
            <!-- Message Section -->
            <div class="message-section">
                <h3>💬 Support Request Message</h3>
                <div class="message-content">{{support_message}}</div>
            </div>
            
            <!-- Timestamp -->
            <div class="timestamp">
                <strong>📅 Submitted on:</strong> {{submission_time}}
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <h3>🚀 Cyber Wolf Support Team</h3>
            
            <div class="contact-info">
                <div class="contact-item">
                    <h4>📧 Email Support</h4>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                
                <div class="contact-item">
                    <h4>📱 Phone Support</h4>
                    <a href="tel:+916374344424">+91 6374 344 424</a><br>
                    <a href="tel:+916379869678">+91 6379 869 678</a>
                </div>
                
                <div class="contact-item">
                    <h4>💬 WhatsApp Community</h4>
                    <a href="https://whatsapp.com/channel/0029VbAmBt7GufIqh9iwND05" target="_blank">Join Community</a>
                </div>
            </div>
            
            <p style="margin-top: 20px; opacity: 0.8;">
                🛡️ Empowering the next generation of cybersecurity professionals
            </p>
            
            <p style="margin-top: 10px; font-size: 0.9em; opacity: 0.7;">
                © 2024 Cyber Wolf - Advanced Cybersecurity Training Platform
            </p>
        </div>
    </div>
</body>
</html>
