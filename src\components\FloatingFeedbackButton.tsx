import React from 'react';
import { MessageSquare, Star } from 'lucide-react';

interface FloatingFeedbackButtonProps {
  onClick: () => void;
}

const FloatingFeedbackButton: React.FC<FloatingFeedbackButtonProps> = ({ onClick }) => {
  return (
    <div className="fixed bottom-6 right-6 z-40">
      <button
        onClick={onClick}
        className="group relative bg-gradient-to-r from-psyco-green-DEFAULT to-green-500 hover:from-psyco-green-dark hover:to-green-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 animate-pulse"
        title="Share Your Feedback"
      >
        <MessageSquare className="h-6 w-6" />
        
        {/* Tooltip */}
        <div className="absolute right-16 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white px-3 py-2 rounded-lg text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap pointer-events-none">
          <div className="flex items-center space-x-2">
            <Star className="h-4 w-4 text-yellow-400" />
            <span>Share Your Feedback</span>
          </div>
          {/* Arrow */}
          <div className="absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-800"></div>
        </div>
        
        {/* Pulse ring */}
        <div className="absolute inset-0 rounded-full bg-psyco-green-DEFAULT opacity-30 animate-ping"></div>
      </button>
    </div>
  );
};

export default FloatingFeedbackButton;
