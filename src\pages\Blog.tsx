
import React, { useEffect, useState } from "react";
import BlogPost from "@/components/BlogPost";
import { Search, Mail, CheckCircle } from "lucide-react";
import { Input } from "@/components/ui/input";
import emailjs from '@emailjs/browser';

const Blog = () => {
  // Scroll to top on page load
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const [searchTerm, setSearchTerm] = useState("");
  const [email, setEmail] = useState("");
  const [isSubscribing, setIsSubscribing] = useState(false);
  const [subscriptionStatus, setSubscriptionStatus] = useState<'idle' | 'success' | 'error'>('idle');

  // EmailJS configuration
  const EMAILJS_SERVICE_ID = 'service_05rrk3a';
  const EMAILJS_TEMPLATE_ID = 'template_9p1ynh2';
  const EMAILJS_PUBLIC_KEY = 'UReJUOuf7nicAZWRS';

  const handleSubscribe = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !email.includes('@')) {
      setSubscriptionStatus('error');
      return;
    }

    setIsSubscribing(true);
    setSubscriptionStatus('idle');

    try {
      const templateParams = {
        user_email: email,
        subscription_date: new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
        to_email: email,
        from_name: 'Cyber Wolf Team',
        message: `Welcome to Cyber Wolf Newsletter! Thank you for subscribing with ${email}. You'll receive the latest cybersecurity insights, tutorials, and industry updates.`
      };

      await emailjs.send(
        EMAILJS_SERVICE_ID,
        EMAILJS_TEMPLATE_ID,
        templateParams,
        EMAILJS_PUBLIC_KEY
      );

      setSubscriptionStatus('success');
      setEmail('');

      // Reset success message after 5 seconds
      setTimeout(() => {
        setSubscriptionStatus('idle');
      }, 5000);

    } catch (error) {
      console.error('EmailJS Error:', error);
      setSubscriptionStatus('error');

      // Reset error message after 5 seconds
      setTimeout(() => {
        setSubscriptionStatus('idle');
      }, 5000);
    } finally {
      setIsSubscribing(false);
    }
  };
  
  const blogPosts = [
    {
      id: "security-research-framework",
      title: "Advanced Security Research Framework: Comprehensive Reconnaissance Guide",
      excerpt: "Complete framework for advanced passive reconnaissance and intelligence gathering with Python automation tools and OSINT integration.",
      date: "January 10, 2024",
      readTime: "18 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://www.engineersgarage.com/wp-content/uploads/2019/07/Representational-Image-For-Computer-Hacking.jpg",
      featured: true
    },
    {
      id: "threat-intelligence-matrix",
      title: "Comprehensive Vulnerability Classification & Threat Intelligence Matrix",
      excerpt: "Advanced threat intelligence framework with CVSS scoring, exploitability analysis, and comprehensive attack vector classification system.",
      date: "January 8, 2024",
      readTime: "15 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTaItztIZUkqPQ8wBbZEaldJErr_nuzS3Mcuw&s"
    },
    {
      id: "advanced-osint-techniques",
      title: "Advanced OSINT Techniques for Security Researchers",
      excerpt: "Professional Open Source Intelligence gathering methods, tools, and automation techniques for comprehensive target analysis.",
      date: "January 5, 2024",
      readTime: "14 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTRtttgK86ASbgLSSFKnwyDsigtCnDoQtZS7w&s"
    },
    {
      id: "web-penetration-testing-fundamentals",
      title: "Web Penetration Testing Fundamentals: A Complete Guide",
      excerpt: "Comprehensive guide to web application penetration testing methodologies, tools, and best practices for identifying security vulnerabilities.",
      date: "December 15, 2023",
      readTime: "12 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://eluminoustechnologies.com/blog/wp-content/uploads/2024/01/1-2.png",
      featured: false
    },
    {
      id: "owasp-top-10-2023",
      title: "OWASP Top 10 2023: Critical Web Application Security Risks",
      excerpt: "Deep dive into the latest OWASP Top 10 vulnerabilities and practical mitigation strategies for web application security.",
      date: "November 28, 2023",
      readTime: "10 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://www.reflectiz.com/wp-content/uploads/2023/08/Blog-page-images-79.jpg"
    },
    {
      id: "cybersecurity-frameworks-compliance",
      title: "Cybersecurity Frameworks and Compliance Standards",
      excerpt: "Understanding NIST, ISO 27001, SOC 2, and other essential cybersecurity frameworks for enterprise security programs.",
      date: "October 20, 2023",
      readTime: "8 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQC3YED5f-lzhBPRace936gYRNgjjUGEhb6Kw&s"
    },
    {
      id: "vulnerability-assessment-methodology",
      title: "Vulnerability Assessment Methodology and Best Practices",
      excerpt: "Step-by-step guide to conducting effective vulnerability assessments using industry-standard tools and methodologies.",
      date: "September 12, 2023",
      readTime: "9 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTkgGMq9xBsPx_AnhyeqZgqOq-yrFCXmVPHCg&s"
    },
    {
      id: "security-auditing-best-practices",
      title: "Security Auditing Best Practices for Enterprise Systems",
      excerpt: "Comprehensive guide to conducting security audits, including compliance requirements, audit methodologies, and reporting standards.",
      date: "August 5, 2023",
      readTime: "11 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "data:image/jpeg;base64,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"
    },
    {
      id: "incident-response-playbook",
      title: "Incident Response Playbook: From Detection to Recovery",
      excerpt: "Essential incident response procedures, team coordination strategies, and recovery methodologies for cybersecurity incidents.",
      date: "July 18, 2023",
      readTime: "13 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://www.magnetforensics.com/wp-content/uploads/2023/02/MF_EnhancingIncidentResponsePlaybook_FrameworkDiagram1600x657.png"
    },
    {
      id: "NodeJS-24.x-Path-Traversal",
      title: "NodeJS 24.x - Path Traversal",
      excerpt: "CVE:2025-27210,Platform: NodeJS",
      date: "July 16, 2025",
      readTime: "8 min read",
      author: "Abdualhadi khalifa",
      category: "Cybersecurity",
      imageSrc: "data:image/png;base64,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",
      featured: true
    },
    {
      id: "Free-Web-Penetration-Testing-Course",
      title: "Free Web Penetration Testing Course",
      excerpt: "The Complete Web Penetration Testing Course and Methods of Finding Vulnerabilities",
      date: "July 18, 2025",
      readTime: "13 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://images.unsplash.com/photo-1666615435088-4865bf5ed3fd?q=80&w=1171&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
    }
  ];
  
  const categories = [
    "All",
    "Cybersecurity"
  ];
  
  const [activeCategory, setActiveCategory] = useState("All");
  
  const filteredPosts = blogPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.author.toLowerCase().includes(searchTerm.toLowerCase());
      
    const matchesCategory = activeCategory === "All" || post.category === activeCategory;
    
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="bg-psyco-black-light py-16 px-6 md:px-12">
        <div className="max-w-7xl mx-auto">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6 animate-fade-in">Blog & Insights</h1>
            <p className="text-xl text-gray-300 mb-8 animate-fade-in animation-delay-100">
              Industry knowledge, technical tips, and event inspiration from our expert team
            </p>
          </div>
          
          <div className="flex flex-col md:flex-row gap-4 items-center animate-fade-in animation-delay-200">
            <div className="relative w-full md:w-1/2">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-black" size={18} />
              <Input
                type="text"
                placeholder="Search articles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-psyco-black-DEFAULT border-psyco-green-muted/50 w-full text-black placeholder:text-black/70"
              />
            </div>
            
            <div className="w-full md:w-1/2 flex gap-2 overflow-x-auto pb-2 no-scrollbar flex-nowrap md:justify-end">
              {categories.map(category => (
                <button
                  key={category}
                  onClick={() => setActiveCategory(category)}
                  className={`px-4 py-2 rounded-full whitespace-nowrap transition-colors ${
                    activeCategory === category
                      ? "bg-psyco-green-DEFAULT text-white"
                      : "bg-psyco-black-DEFAULT text-gray-300 hover:bg-psyco-black-card"
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>
      
      {/* Blog Posts */}
      <section className="py-16 px-6 md:px-12">
        <div className="max-w-7xl mx-auto">
          {filteredPosts.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredPosts.map((post, index) => (
                <BlogPost
                  key={post.id}
                  {...post}
                  className={`animate-fade-in ${post.featured ? "md:col-span-2" : ""}`}
                  style={{ animationDelay: `${index * 100}ms` }}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <h3 className="text-xl text-white mb-2">No posts found</h3>
              <p className="text-gray-400">Try adjusting your search or category filter</p>
            </div>
          )}
        </div>
      </section>
      
      {/* Newsletter Section */}
      <section className="py-16 px-6 md:px-12 bg-psyco-black-light">
        <div className="max-w-7xl mx-auto">
          <div className="glassmorphism p-8 md:p-12 text-center">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Mail className="text-psyco-green-DEFAULT" size={32} />
              <h2 className="text-2xl md:text-3xl font-bold text-white">Stay Updated with Industry Insights</h2>
            </div>
            <p className="text-gray-300 max-w-2xl mx-auto mb-8">
              Subscribe to our newsletter to receive the latest articles, tips, and industry news directly in your inbox.
            </p>

            {/* Subscription Status Messages */}
            {subscriptionStatus === 'success' && (
              <div className="mb-6 p-4 bg-green-500/20 border border-green-500/50 rounded-lg flex items-center justify-center gap-2">
                <CheckCircle className="text-green-400" size={20} />
                <span className="text-green-400 font-medium">
                  Successfully subscribed! Check your email for confirmation.
                </span>
              </div>
            )}

            {subscriptionStatus === 'error' && (
              <div className="mb-6 p-4 bg-red-500/20 border border-red-500/50 rounded-lg">
                <span className="text-red-400 font-medium">
                  Subscription failed. Please check your email and try again.
                </span>
              </div>
            )}

            <form onSubmit={handleSubscribe} className="flex flex-col sm:flex-row gap-4 max-w-lg mx-auto">
              <Input
                type="email"
                placeholder="Your email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="bg-psyco-black-DEFAULT border-psyco-green-muted/50 flex-grow text-black placeholder:text-black/70"
                required
                disabled={isSubscribing}
              />
              <button
                type="submit"
                disabled={isSubscribing || !email}
                className="bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-medium py-2 px-6 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 min-w-[120px]"
              >
                {isSubscribing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    <span>Subscribing...</span>
                  </>
                ) : (
                  <>
                    <Mail size={16} />
                    <span>Subscribe</span>
                  </>
                )}
              </button>
            </form>

            <p className="text-gray-400 text-sm mt-4">
              We respect your privacy. Unsubscribe at any time.
            </p>

            {/* Newsletter Features */}
            <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="flex items-center justify-center gap-2 text-gray-300">
                <span className="text-psyco-green-DEFAULT">🔒</span>
                <span>Security Insights</span>
              </div>
              <div className="flex items-center justify-center gap-2 text-gray-300">
                <span className="text-psyco-green-DEFAULT">📚</span>
                <span>Latest Tutorials</span>
              </div>
              <div className="flex items-center justify-center gap-2 text-gray-300">
                <span className="text-psyco-green-DEFAULT">🎯</span>
                <span>Exclusive Content</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Blog;
